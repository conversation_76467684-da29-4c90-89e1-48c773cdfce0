
"""
This module defines the DagBuilder class, a utility for simplifying the creation of
Airflow DAGs that execute remote tasks over SSH on our edge nodes. It encapsulates logic for:

- Inferring DAG IDs and SSH connection keys from file paths.
- Fetching environment-specific configuration from Airflow Variables.
- Creating DAGs with standardized retry logic and notification callbacks.
- Generating SSH-based tasks using a reusable task runner.

"""
import sys
import os
sys.path.append(os.path.dirname(__file__))
from airflow import DAG
from airflow.utils.dates import days_ago
from airflow.models import Variable
from datetime import timedelta
from airflow.operators.python import PythonOperator
from utilities.send_sns_notification import (
    get_on_success_callback,
    get_on_failure_callback,
    get_on_retry_callback,
)
from utilities.task_runner import sshTask


class DagBuilder:
    def __init__(self, schedule=None, retries=1, start_days_ago_val=1, dag_file_path=None):
        self.dag_file_path = dag_file_path or __file__
        self.dag_id = self._infer_dag_id(self.dag_file_path)
        self.schedule = schedule
        self.retries = retries
        self.start_days_ago_val = start_days_ago_val
        self.ssh_conn_id_key = self._infer_ssh_conn_id_key(self.dag_file_path)
        self.env = Variable.get("ENV")
        self.conn_id, self.server_name = self._get_connection_info()

    @staticmethod
    def _infer_ssh_conn_id_key(dag_file_path: str) -> str:
        return os.path.basename(os.path.dirname(dag_file_path))

    @staticmethod
    def _infer_dag_id(dag_file_path: str) -> str:
        return os.path.splitext(os.path.basename(dag_file_path))[0]

    def _get_connection_info(self):
        conn_string = Variable.get(self.ssh_conn_id_key)
        return conn_string.split("|") if "|" in conn_string else (conn_string, "unknown-server")

    def create_dag(self):
        default_args = {
            "owner": "airflow",
            "retries": self.retries,
            "retry_delay": timedelta(minutes=1),
            "email_on_failure": True,
            "email_on_retry": True,
            "email_on_success": True,
        }

        dag = DAG(
            dag_id=self.dag_id,
            default_args=default_args,
            schedule_interval=self.schedule,
            start_date=days_ago(self.start_days_ago_val),
            catchup=False,
            on_success_callback=get_on_success_callback(self.server_name, self.env),
            on_failure_callback=get_on_failure_callback(self.server_name, self.env),
        )
        self.dag = dag
        return dag

    def create_ssh_task(self, script_path,on_failure_callback=None):
        print("connectio name",self.conn_id)
        task = sshTask(self.dag, script_path, self.conn_id, on_failure_callback=on_failure_callback)
        task.on_retry_callback = get_on_retry_callback(self.server_name, self.env)
        return task

    def create_validation_task(self, ssh_task_id: str):
        """Creates a PythonOperator to validate the SSH command output via XCom."""

        def check_ssh_output(**context):
            output = context['ti'].xcom_pull(task_ids=context['params']['ssh_task_id'])
            if output is None or 'failed' in output.lower():
                raise ValueError("Shell script execution failed.")
            print("Command output:\n", output)

        return PythonOperator(
            task_id="validate_ssh_output",
            python_callable=check_ssh_output,
            params={"ssh_task_id": ssh_task_id},
            dag=self.dag,
        )