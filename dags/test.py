from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
import requests

def test_outbound():
    response = requests.get("https://www.google.com")
    print("Status Code:", response.status_code)

with DAG("test_outbound_connectivity", start_date=days_ago(1), schedule_interval=None) as dag:
    task = PythonOperator(
        task_id="check_google",
        python_callable=test_outbound
    )
