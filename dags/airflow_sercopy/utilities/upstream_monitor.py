"""
Utility module for monitoring upstream task failures and sending notifications.
This module provides functionality to create monitoring tasks that check for
upstream failures and send appropriate SNS notifications.
"""

import sys
import os
# Add the parent directory to the path to ensure imports work
current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule

# Define notification function directly to avoid import issues
def send_upstream_failure_notification(context, server_name, env):
    """Send SNS notification for upstream failures"""
    from airflow.providers.amazon.aws.hooks.sns import SnsHook
    from airflow.utils.state import State

    sns_hook = SnsHook(aws_conn_id="AWS_SNS", region_name="us-east-1")
    ti = context['task_instance']
    dag_id = context['dag'].dag_id
    task_id = ti.task_id
    run_id = context.get('run_id')
    try_number = ti.try_number
    execution_date = str(context.get('execution_date'))
    log_url = ti.log_url
    operator = str(ti.operator)
    duration = ti.duration or "N/A"
    hostname = ti.hostname or "N/A"

    # Set status based on task state
    task_state = ti.state
    if task_state == State.UPSTREAM_FAILED:
        status_text = "UPSTREAM_FAILED"
    else:
        status_text = "FAIL"

    subject = f"{status_text}: {env} {dag_id}"

    message = (
        f"Status: {status_text}\n"
        f"DAG: {dag_id}\n"
        f"Task: {task_id}\n"
        f"Run ID: {run_id}\n"
        f"Execution Date: {execution_date}\n"
        f"Try Number: {try_number}\n"
        f"Operator: {operator}\n"
        f"Duration: {duration} sec\n"
        f"Hostname: {hostname}\n"
        f"Environment: {env}\n"
        f"Server: {server_name}\n"
        f"Logs: {log_url}\n"
    )

    sns_topic_arn = "arn:aws:sns:us-east-1:308705121675:sns"  # Email
    slack_topic_arn = "arn:aws:sns:us-east-1:308705121675:snstopic"  # Slack

    # Send SNS email notification (always)
    try:
        sns_hook.publish_to_target(
            target_arn=sns_topic_arn,
            message=message,
            subject=subject
        )
        print("[INFO] SNS email notification sent.")
    except Exception as e:
        print(f"[ERROR] Failed to send SNS email: {e}")

    # Send Slack notification for failures
    try:
        sns_hook.publish_to_target(
            target_arn=slack_topic_arn,
            message=message,
            subject=subject
        )
        print("[INFO] SNS Slack notification sent.")
    except Exception as e:
        print(f"[ERROR] Failed to send SNS Slack message: {e}")


def create_upstream_failure_monitor(dag, ssh_task, dag_builder):
    """
    Creates a monitoring task that checks for upstream failures and sends notifications.
    
    Args:
        dag: The Airflow DAG object
        ssh_task: The SSH task to monitor
        dag_builder: The DagBuilder instance containing server and environment info
    
    Returns:
        PythonOperator: A monitoring task that runs regardless of upstream status
    """
    
    def check_and_notify_upstream_failure(**context):
        """Check if SSH task failed due to upstream failures and send notification"""
        from airflow.models import TaskInstance
        from airflow.utils.state import State
        from airflow.utils.db import provide_session
        
        @provide_session
        def check_task_state(session=None):
            # Get the SSH task instance
            ssh_task_id = ssh_task.task_id
            dag_run = context['dag_run']
            
            # Query the SSH task instance
            ti = session.query(TaskInstance).filter(
                TaskInstance.dag_id == dag_run.dag_id,
                TaskInstance.task_id == ssh_task_id,
                TaskInstance.execution_date == dag_run.execution_date
            ).first()
            
            if ti and ti.state == State.UPSTREAM_FAILED:
                print(f"SSH task {ssh_task_id} failed due to upstream failures. Sending notification...")
                
                # Create a mock context for the SSH task to send notification
                ssh_context = context.copy()
                ssh_context['task_instance'] = ti
                
                # Send notification
                send_upstream_failure_notification(
                    context=ssh_context,
                    server_name=dag_builder.server_name,
                    env=dag_builder.env
                )
                print("Upstream failure notification sent successfully.")
            else:
                print(f"SSH task state: {ti.state if ti else 'Not found'}")
        
        return check_task_state()
    
    # Create monitoring task that runs regardless of upstream status
    monitor_task = PythonOperator(
        task_id='monitor_upstream_failures',
        python_callable=check_and_notify_upstream_failure,
        trigger_rule=TriggerRule.ALL_DONE,  # Run regardless of upstream status
        dag=dag
    )
    
    return monitor_task


def create_generic_upstream_failure_monitor(dag, monitored_task_id, server_name, env):
    """
    Creates a generic monitoring task that can monitor any task for upstream failures.
    
    Args:
        dag: The Airflow DAG object
        monitored_task_id: The task ID to monitor for upstream failures
        server_name: Server name for notifications
        env: Environment name for notifications
    
    Returns:
        PythonOperator: A monitoring task that runs regardless of upstream status
    """
    
    def check_and_notify_upstream_failure(**context):
        """Check if monitored task failed due to upstream failures and send notification"""
        from airflow.models import TaskInstance
        from airflow.utils.state import State
        from airflow.utils.db import provide_session
        
        @provide_session
        def check_task_state(session=None):
            dag_run = context['dag_run']
            
            # Query the monitored task instance
            ti = session.query(TaskInstance).filter(
                TaskInstance.dag_id == dag_run.dag_id,
                TaskInstance.task_id == monitored_task_id,
                TaskInstance.execution_date == dag_run.execution_date
            ).first()
            
            if ti and ti.state == State.UPSTREAM_FAILED:
                print(f"Task {monitored_task_id} failed due to upstream failures. Sending notification...")
                
                # Create a mock context for the monitored task to send notification
                task_context = context.copy()
                task_context['task_instance'] = ti
                
                # Send notification
                send_upstream_failure_notification(
                    context=task_context,
                    server_name=server_name,
                    env=env
                )
                print("Upstream failure notification sent successfully.")
            else:
                print(f"Task {monitored_task_id} state: {ti.state if ti else 'Not found'}")
        
        return check_task_state()
    
    # Create monitoring task that runs regardless of upstream status
    monitor_task = PythonOperator(
        task_id=f'monitor_{monitored_task_id}_upstream_failures',
        python_callable=check_and_notify_upstream_failure,
        trigger_rule=TriggerRule.ALL_DONE,  # Run regardless of upstream status
        dag=dag
    )
    
    return monitor_task
