from airflow.providers.ssh.operators.ssh import SSHOperator
from airflow.models import Variable

def sshTask(dag, script_path, ssh_conn_id="cdp_sqlscript",on_failure_callback=None):

    """ This function sets up an SSHOperator to run scripts on AWS Lake edgenodes, assuming sudo user & etl_app role 
        from airflow variables. SSHOperator uses specified SSH connection to execute the command, with specified timeouts.
        It generates a task ID based on the DAG ID and gives the command to be executed.   
    """
   
    sudo_cmd = Variable.get("sudo_cmd")
    sh_cmd = script_path

    # If Sudo Command Exists, Execute as sudo User
    if sudo_cmd != "":
        cmd = f"{sudo_cmd} '{sh_cmd}'"
    else:
        cmd = sh_cmd
        
    sshOp = SSHOperator(
        task_id=dag.dag_id.replace('_dag', '_task'),
        command=cmd,
        ssh_conn_id=ssh_conn_id,
        cmd_timeout=86400,
        conn_timeout=120,
        dag=dag,
        on_failure_callback=on_failure_callback
    )

    return sshOp