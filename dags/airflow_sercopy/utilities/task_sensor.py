from airflow.sensors.external_task import ExternalTaskSensor
from airflow.utils.dates import days_ago
from datetime import timedelta

def sensor(ext_dag, dag, minutes_delta=0, on_failure_callback=None, external_task_id=None):
    """
    Creates an ExternalTaskSensor that waits for the latest run of an external DAG task to complete.
    Uses execution_date_fn to check the most recent DAG run instead of being tied to specific dates.
    """

    # Auto-discover task ID if not provided
    if not external_task_id:
        # Use common pattern: replace '_dag' with '_task'
        external_task_id = ext_dag.replace('_dag', '_task')

    def get_most_recent_dag_run(execution_date, **context):
        """
        Function to get the most recent DAG run execution date.
        This allows the sensor to check the latest run instead of a specific execution date.
        """
        from airflow.models import DagRun
        from airflow.utils.db import provide_session

        @provide_session
        def get_latest_execution_date(session=None):
            # Get the most recent successful or running DAG run
            latest_run = session.query(DagRun).filter(
                DagRun.dag_id == ext_dag,
                DagRun.state.in_(['success', 'running', 'failed'])
            ).order_by(DagRun.execution_date.desc()).first()

            if latest_run:
                print(f"Found latest run for {ext_dag}: {latest_run.execution_date}")
                # Apply minutes_delta if specified
                if minutes_delta > 0:
                    adjusted_date = latest_run.execution_date + timedelta(minutes=minutes_delta)
                    print(f"Adjusted execution date by {minutes_delta} minutes: {adjusted_date}")
                    return adjusted_date
                return latest_run.execution_date
            else:
                # If no runs found, return a date in the past to avoid blocking
                print(f"No recent runs found for {ext_dag}, using past date")
                return days_ago(1)

        return get_latest_execution_date()

    # Create ExternalTaskSensor
    return ExternalTaskSensor(
        task_id=f"wait_for_{ext_dag.replace(' ', '_')}",
        external_dag_id=ext_dag,
        external_task_id=external_task_id,
        execution_date_fn=get_most_recent_dag_run,
        timeout=300,  # 5 minutes timeout
        poke_interval=30,  # Check every 30 seconds
        mode='poke',
        dag=dag,
        on_failure_callback=on_failure_callback,  # Will be None by default
        retries=0
    )