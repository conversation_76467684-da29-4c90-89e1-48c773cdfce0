from airflow.operators.python import PythonOperator

def sensor(ext_dag, dag, minutes_delta=0, on_failure_callback=None, external_task_id=None):
    """
    MWAA-compatible sensor using PythonOperator that checks the latest run of an external DAG.
    This approach is more reliable in MWAA than custom sensor classes.
    """

    def check_external_dag_status(**context):
        """Check the latest external DAG run status"""
        from airflow.models import DagRun, TaskInstance
        from airflow.utils.db import provide_session

        @provide_session
        def get_external_status(session=None):
            # Get the most recent DAG run
            latest_run = session.query(DagRun).filter(
                DagRun.dag_id == ext_dag
            ).order_by(DagRun.execution_date.desc()).first()

            if not latest_run:
                raise Exception(f"No DAG runs found for {ext_dag}")

            print(f"Latest DAG run: {latest_run.run_id}, State: {latest_run.state}")

            # Auto-discover task ID if not provided
            task_id = external_task_id
            if not task_id:
                # Get task IDs from latest run
                tasks = session.query(TaskInstance.task_id).filter(
                    TaskInstance.dag_id == ext_dag,
                    TaskInstance.execution_date == latest_run.execution_date
                ).distinct().all()

                if tasks:
                    task_ids = [t.task_id for t in tasks]
                    print(f"Found tasks in latest run: {task_ids}")

                    # For single task, use it
                    if len(task_ids) == 1:
                        task_id = task_ids[0]
                    else:
                        # Prefer _task suffix
                        for tid in task_ids:
                            if tid.endswith('_task'):
                                task_id = tid
                                break
                        if not task_id:
                            task_id = task_ids[0]

                    print(f"Selected task ID: {task_id}")
                else:
                    # Fallback to pattern
                    task_id = ext_dag.replace('_dag', '_task')
                    print(f"Using fallback task ID: {task_id}")

            # Check task status in latest run
            task_instance = session.query(TaskInstance).filter(
                TaskInstance.dag_id == ext_dag,
                TaskInstance.task_id == task_id,
                TaskInstance.execution_date == latest_run.execution_date
            ).first()

            if not task_instance:
                raise Exception(f"Task {task_id} not found in latest run of {ext_dag}")

            print(f"Task {task_id} state: {task_instance.state}")

            # Check task status
            if task_instance.state == 'success':
                print(f"✅ Task {task_id} is successful!")
                return True
            elif task_instance.state == 'failed':
                raise Exception(f"❌ External task {ext_dag}.{task_id} failed in latest run")
            else:
                raise Exception(f"⏳ Task {task_id} is not successful (state: {task_instance.state}). Please wait for completion or check external DAG.")

        return get_external_status()

    # Create PythonOperator that checks external status
    # No failure callback by default - sensors won't send notifications
    return PythonOperator(
        task_id=f"wait_for_{ext_dag.replace(' ', '_')}",
        python_callable=check_external_dag_status,
        dag=dag,
        on_failure_callback=on_failure_callback,  # Will be None by default
        retries=0
    )