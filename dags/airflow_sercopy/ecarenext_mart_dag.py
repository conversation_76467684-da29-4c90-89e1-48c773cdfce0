
import sys
import os
sys.path.append(os.path.dirname(__file__))
from airflow import DAG
from dag_utils_class import DagBuilder
from utilities.task_sensor import sensor
from utilities.upstream_monitor import create_upstream_failure_monitor

schedule = "50 9 * * *"
script_path = "/home/<USER>/master_script.sh"

dag_builder = DagBuilder(schedule=schedule, dag_file_path=__file__)
dag = dag_builder.create_dag()

# Define sensor configurations

sensor_configs = [
   {"ext_dag": "run_intellisource_wc_patientaccounts_etl_dag"},
   {"ext_dag": "analytics_groups_cron_dag"},
   {"ext_dag": "run_intellisource_etl_dag", "minutes_delta": 2},
]

sensor_tasks = [
   sensor(**config, dag=dag)
   for config in sensor_configs
]

# Create SSH task using DagBuilder
ssh_task = dag_builder.create_ssh_task(script_path)

# Create monitoring task for upstream failures
monitor_task = create_upstream_failure_monitor(dag, ssh_task, dag_builder)

with dag:
   sensor_tasks >> ssh_task
   sensor_tasks >> monitor_task  # Monitor task also depends on sensors


