
import sys
import os
sys.path.append(os.path.dirname(__file__))
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from dag_utils_class import DagBuilder
from utilities.task_sensor import sensor
from utilities.send_sns_notification import send_combined_notification

schedule = "50 9 * * *"
script_path = "/home/<USER>/master_script.sh"

dag_builder = DagBuilder(schedule=schedule, dag_file_path=__file__)
dag = dag_builder.create_dag()

# Define sensor configurations

sensor_configs = [
   {"ext_dag": "run_intellisource_wc_patientaccounts_etl_dag"},
   {"ext_dag": "analytics_groups_cron_dag"},
   {"ext_dag": "run_intellisource_etl_dag", "minutes_delta": 2},
]

sensor_tasks = [
   sensor(**config, dag=dag)
   for config in sensor_configs
]

# Create SSH task using DagBuilder
ssh_task = dag_builder.create_ssh_task(script_path)

# Create a monitoring task that checks for upstream failures and sends notifications
def check_and_notify_upstream_failure(**context):
    """Check if SSH task failed due to upstream failures and send notification"""
    from airflow.models import TaskInstance
    from airflow.utils.state import State
    from airflow.utils.db import provide_session

    @provide_session
    def check_task_state(session=None):
        # Get the SSH task instance
        ssh_task_id = ssh_task.task_id
        dag_run = context['dag_run']

        # Query the SSH task instance
        ti = session.query(TaskInstance).filter(
            TaskInstance.dag_id == dag_run.dag_id,
            TaskInstance.task_id == ssh_task_id,
            TaskInstance.execution_date == dag_run.execution_date
        ).first()

        if ti and ti.state == State.UPSTREAM_FAILED:
            print(f"SSH task {ssh_task_id} failed due to upstream failures. Sending notification...")

            # Create a mock context for the SSH task to send notification
            ssh_context = context.copy()
            ssh_context['task_instance'] = ti

            # Send notification
            send_combined_notification(
                context=ssh_context,
                status="fail",
                server_name=dag_builder.server_name,
                env=dag_builder.env
            )
            print("Upstream failure notification sent successfully.")
        else:
            print(f"SSH task state: {ti.state if ti else 'Not found'}")

    return check_task_state()

# Create monitoring task that runs regardless of upstream status
monitor_task = PythonOperator(
    task_id='monitor_upstream_failures',
    python_callable=check_and_notify_upstream_failure,
    trigger_rule=TriggerRule.ALL_DONE,  # Run regardless of upstream status
    dag=dag
)

with dag:
   sensor_tasks >> ssh_task
   sensor_tasks >> monitor_task  # Monitor task also depends on sensors


