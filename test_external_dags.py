from airflow import DAG
from airflow.providers.amazon.aws.operators.emr import (
    EmrCreateJobFlowOperator,
    EmrAddStepsOperator,
    EmrTerminateJobFlowOperator
)
from airflow.utils.dates import days_ago
from airflow.utils.task_group import TaskGroup
from datetime import timedelta

# ------------------ Config ------------------
JAR_PATH = "s3://ddl-dev-airflow/jar/marklogic-load-1.2.1.jar"
BUCKET = "cas-edm-edh"
KEY = "data/prod/wholebmv/release/2025-4/build/2/"
EC2_KEY_NAME = "ddl-dev-ml-loader"

ENV_VARS = {
    "mlHost": "*************,**************,*************,*************,*************,**************,"
              "**************,*************,**************,**************,*************,**************,"
              "*************,*************,**************,**************,**************,**************,"
              "**************,*************",
    "port": "8006",
    "mlUser": "core-chemistry-loader",
    "mlPassword": "core-chemistry-loader",
    "ssl": "False",
    "loadBalancer": "False",
    "artifactVersion": "1.0.0-SNAPSHOT"
}

input_path = f"s3://{BUCKET}/{KEY}"

# EMR Cluster configuration (no instance fleet)
JOB_FLOW_OVERRIDES = {
    "Name": "wbmv_marklogic_loader",
    "ReleaseLabel": "emr-6.12.0",
    "Applications": [{"Name": "Spark"}],
    "Instances": {
        "Ec2KeyName": EC2_KEY_NAME,
        "KeepJobFlowAliveWhenNoSteps": True,
        "TerminationProtected": False,
        "Ec2SubnetId": "subnet-0211749808b02da02",
        "EmrManagedMasterSecurityGroup": "sg-02612b6ae836ed739",
        "EmrManagedSlaveSecurityGroup": "sg-03331bd956b9c6dbd",
        "ServiceAccessSecurityGroup": "sg-0172b9f7c7188f72a"
    },
    "JobFlowRole": "EMR_EC2_DefaultRole",
    "ServiceRole": "EMR_DefaultRole",
    "VisibleToAllUsers": True,
    "LogUri": "s3://ddl-dev-airflow/emr_logs/",
    "Tags": [
        {"Key": "CREATED_BY", "Value": "czm11"},
        {"Key": "for-use-with-amazon-emr-managed-policies", "Value": "true"}
    ]
}

# Sequential and Parallel domain jobs
SEQUENTIAL_DOMAINS = [
    {"domain": "finding", "collection": "document-finding"},
    {"domain": "substance.properties", "collection": "physical-properties-and-spectra"},
    {"domain": "reaction", "collection": "reaction"},
    {"domain": "substance", "collection": "substance"},
    {"domain": "patent.family.immediate", "collection": "patent-family-immediate"},
    {"domain": "patent.family.simple", "collection": "patent-family-simple"},
    {"domain": "patent.lifecycle.group", "collection": "patent-lifecycle-group"},
    {"domain": "multistep.reaction", "collection": "multistep-reaction"},
    {"domain": "document", "collection": "document"}
]

PARALLEL_DOMAINS = [
    {"domain": "retrosynthetic.reaction.rule", "collection": "retrosynthetic-reaction-rule"},
    {"domain": "specialty.chemical", "collection": "specialty-chemical"},
    {"domain": "hazard.designation", "collection": "hazard-designation"},
    {"domain": "thesaurus.bioactivity", "collection": "thesaurus-bioactivity"},
    {"domain": "external.classification", "collection": "external-classification"},
    {"domain": "external.thesaurus.member", "collection": "external-thesaurus-member"},
    {"domain": "external.thesaurus", "collection": "external-thesaurus"},
    {"domain": "external.vocabulary.entry", "collection": "external-vocabulary-entry"},
    {"domain": "chemical.formulation", "collection": "chemical-formulation"},
    {"domain": "commsource.catalog.issue", "collection": "catalog-issue"},
    {"domain": "commsource.catalog", "collection": "catalog"},
    {"domain": "genthesaurus", "collection": "genthesaurus"},
    {"domain": "commsource.supplier", "collection": "supplier"},
    {"domain": "commsource.catalog.item", "collection": "catalog-item"},
    {"domain": "company.name", "collection": "company-name"},
    {"domain": "list", "collection": "list"},
    {"domain": "listed.item.instance", "collection": "listed-item-instance"},
    {"domain": "markush", "collection": "markush"},
    {"domain": "method", "collection": "method"},
    {"domain": "regulated.item", "collection": "regulated-item"},
    {"domain": "regulatory.list.item.instance", "collection": "regulatory-list-item-instance"},
    {"domain": "regulatory.list", "collection": "regulatory-list"},
    {"domain": "thesaurus.proteintarget", "collection": "thesaurus-proteintarget"},
    {"domain": "history", "collection": "history"},
    {"domain": "patent.owner.publication", "collection": "patent-owner-publication"},
    {"domain": "vocabulary", "collection": "vocabulary"}
]

# Step Builder
def create_emr_step(domain, collection, is_parallel):
    memory = "2G" if is_parallel else "10G"
    driver_memory = "2G" if is_parallel else "15G"
    cores = "1" if is_parallel else "4"
    cleaned_path = input_path.replace("/_COPY_COMPLETE", "")

    return {
        "Name": f"update-{domain}",
        "ActionOnFailure": "CONTINUE",
        "HadoopJarStep": {
            "Jar": "command-runner.jar",
            "Args": [
                "/usr/bin/spark-submit",
                "--class", "org.cas.dai.wbmvLoad.Main",
                "--master", "yarn",
                "--deploy-mode", "cluster",
                "--executor-cores", cores,
                "--executor-memory", memory,
                "--driver-memory", driver_memory,
                "--conf", "spark.dynamicAllocation.enabled=true",
                JAR_PATH,
                "--host", ENV_VARS["mlHost"],
                "--port", ENV_VARS["port"],
                "--user", ENV_VARS["mlUser"],
                "--password", ENV_VARS["mlPassword"],
                "--partitions", "192",
                "--wbmvDeliveryPath", cleaned_path,
                "--domain", domain,
                "--collection", collection,
                "--batchSize", "300",
                "--ssl", ENV_VARS["ssl"],
                "--loadBalancer", ENV_VARS["loadBalancer"]
            ]
        }
    }

# DAG Definition
default_args = {
    "owner": "airflow",
    "start_date": days_ago(1),
    "retries": 1,
    "retry_delay": timedelta(minutes=5)
}

with DAG(
    dag_id="emr_cluster_full_lifecycle_dag_v2",
    default_args=default_args,
    schedule_interval=None,
    catchup=False
) as dag:

    create_cluster = EmrCreateJobFlowOperator(
        task_id="create_emr_cluster",
        job_flow_overrides=JOB_FLOW_OVERRIDES,
        aws_conn_id="aws_default"
    )

    # Parallel steps with pool to control concurrency
    with TaskGroup("parallel_group") as parallel_group:
        for d in PARALLEL_DOMAINS:
            step = create_emr_step(d["domain"], d["collection"], is_parallel=True)
            EmrAddStepsOperator(
                task_id=f"add_step_{d['domain'].replace('.', '_')}",
                job_flow_id="{{ task_instance.xcom_pull(task_ids='create_emr_cluster', key='return_value') }}",
                steps=[step],
                aws_conn_id="aws_default",
                wait_for_completion=True,
                waiter_max_attempts=1000,
                waiter_delay=30,
                pool="emr_parallel_pool"  # Pool to restrict to 4 parallel tasks
            )

    # Sequential steps in order
    with TaskGroup("sequential_group") as sequential_group:
        prev_task = None
        for d in SEQUENTIAL_DOMAINS:
            step = create_emr_step(d["domain"], d["collection"], is_parallel=False)
            current = EmrAddStepsOperator(
                task_id=f"add_step_{d['domain'].replace('.', '_')}",
                job_flow_id="{{ task_instance.xcom_pull(task_ids='create_emr_cluster', key='return_value') }}",
                steps=[step],
                aws_conn_id="aws_default",
                wait_for_completion=True,
                waiter_max_attempts=1000,
                waiter_delay=30
            )
            if prev_task:
                prev_task >> current
            prev_task = current

    terminate_cluster = EmrTerminateJobFlowOperator(
        task_id="terminate_emr_cluster",
        job_flow_id="{{ task_instance.xcom_pull(task_ids='create_emr_cluster', key='return_value') }}",
        aws_conn_id="aws_default",
        trigger_rule="all_done"
    )

    create_cluster >> parallel_group >> sequential_group >> terminate_cluster
